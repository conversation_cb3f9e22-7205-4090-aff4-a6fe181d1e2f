# Déploiement en Environnement de Recette - FireSplash v1.4.0

## 🚀 Informations de Déploiement

### Version Déployée
- **Version**: 1.4.0
- **Branche**: `staging/v1.4.0-dual-input`
- **Fonctionnalité**: Système dual-input pour localisation des feux

### URLs de Déploiement

#### Production (Main)
- **URL**: https://firesplash.netlify.app
- **Branche**: `main`
- **Statut**: Stable

#### Recette/Staging
- **URL**: https://staging--firesplash.netlify.app (ou URL générée par Netlify)
- **Branche**: `staging/v1.4.0-dual-input`
- **Statut**: En test

## 🆕 Nouvelles Fonctionnalités v1.4.0

### Système Dual-Input
1. **Sélection par Commune** (existant)
   - Recherche par nom de commune
   - Dropdown avec suggestions

2. **Coordonnées GPS** (nouveau)
   - Saisie manuelle latitude/longitude
   - Validation en temps réel
   - Format décimal requis

3. **Clic sur Carte** (nouveau)
   - Clic direct sur la carte interactive
   - Marqueur visuel avec emoji feu 🔥
   - Coordonnées automatiquement remplies

### Améliorations UX
- Indicateurs visuels de méthode active
- Feedback immédiat sur les sélections
- Interface unifiée avec onglets
- Compatibilité totale avec l'existant

## 🧪 Tests à Effectuer

### Tests Fonctionnels

#### 1. Sélection par Commune
- [ ] Recherche d'une commune existante
- [ ] Sélection dans la dropdown
- [ ] Calculs corrects affichés
- [ ] Carte mise à jour avec marqueurs

#### 2. Coordonnées GPS
- [ ] Saisie de coordonnées valides (ex: 43.7750, 6.2000)
- [ ] Validation des erreurs (coordonnées invalides)
- [ ] Bouton "Définir la position du feu" fonctionnel
- [ ] Calculs corrects pour position GPS

#### 3. Clic sur Carte
- [ ] Clic sur la carte active le marqueur
- [ ] Coordonnées automatiquement remplies
- [ ] Calculs déclenchés automatiquement
- [ ] Basculement vers onglet coordonnées

#### 4. Indicateurs Visuels
- [ ] Méthode active clairement indiquée
- [ ] Couleurs différentes (vert=commune, rouge=GPS)
- [ ] Informations de position affichées
- [ ] Instructions d'utilisation visibles

### Tests de Compatibilité

#### Fonctionnalités Existantes
- [ ] Paramètres utilisateur conservés
- [ ] Sauvegarde des simulations
- [ ] Calculs identiques pour communes
- [ ] Interface responsive
- [ ] Navigation entre pages

#### Navigateurs
- [ ] Chrome/Edge (dernières versions)
- [ ] Firefox (dernière version)
- [ ] Safari (si disponible)
- [ ] Mobile (iOS/Android)

### Tests de Performance
- [ ] Temps de chargement initial
- [ ] Réactivité des clics sur carte
- [ ] Validation en temps réel fluide
- [ ] Calculs rapides pour coordonnées

## 🐛 Points d'Attention

### Validation des Coordonnées
- Vérifier que les coordonnées sont dans les limites géographiques
- Tester avec des valeurs limites (-90/90, -180/180)
- Vérifier le comportement avec des décimales nombreuses

### Intégration Carte
- S'assurer que Leaflet fonctionne correctement
- Vérifier les tuiles de carte se chargent
- Tester le zoom et la navigation

### Calculs
- Comparer les résultats commune vs coordonnées pour même position
- Vérifier que les distances sont cohérentes
- Tester avec différentes positions géographiques

## 📝 Feedback et Bugs

### Comment Reporter un Bug
1. **Environnement**: Préciser navigateur et version
2. **Étapes**: Décrire la reproduction du problème
3. **Résultat attendu vs obtenu**
4. **Captures d'écran** si pertinentes

### Canaux de Communication
- GitHub Issues: https://github.com/Eric-Hei/firesplash/issues
- Email: [votre email]
- Slack/Teams: [canal approprié]

## 🔄 Processus de Validation

### Étapes de Validation
1. **Tests fonctionnels** ✅ (à compléter)
2. **Tests de compatibilité** ⏳ (en cours)
3. **Tests de performance** ⏳ (en cours)
4. **Validation utilisateur** ⏳ (en attente)
5. **Déploiement production** ⏳ (en attente)

### Critères d'Acceptation
- [ ] Toutes les fonctionnalités existantes préservées
- [ ] Nouvelles fonctionnalités opérationnelles
- [ ] Aucune régression détectée
- [ ] Performance acceptable
- [ ] Interface utilisateur intuitive

## 🚀 Prochaines Étapes

1. **Tests complets** sur l'environnement de recette
2. **Collecte de feedback** utilisateurs
3. **Corrections** si nécessaires
4. **Validation finale**
5. **Déploiement en production**

---

**Date de déploiement**: [Date actuelle]
**Responsable**: Eric H
**Version**: 1.4.0 - Système Dual-Input
