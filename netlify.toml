[build]
  publish = "dist"
  command = "npm run build"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[dev]
  command = "npm run dev"
  port = 5173
  publish = "dist"

# Configuration pour l'environnement de recette
[context.staging]
  command = "npm run build"
  publish = "dist"

[context.staging.environment]
  NODE_VERSION = "18"
  VITE_APP_ENV = "staging"

# Configuration pour les branches de staging
[context.branch-deploy]
  command = "npm run build"
  publish = "dist"

[context.branch-deploy.environment]
  NODE_VERSION = "18"
  VITE_APP_ENV = "staging"
